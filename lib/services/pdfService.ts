import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

export class PDFService {
  /**
   * Convert rich text HTML content to PDF and download it
   */
  static async downloadRichTextAsPDF(
    htmlContent: string,
    filename: string,
    patientName?: string,
    imageFilename?: string
  ): Promise<void> {
    try {
      // Create a temporary container for the content
      const tempContainer = document.createElement('div');
      tempContainer.style.position = 'absolute';
      tempContainer.style.left = '-9999px';
      tempContainer.style.top = '-9999px';
      tempContainer.style.width = '800px';
      tempContainer.style.padding = '40px';
      tempContainer.style.backgroundColor = 'white';
      tempContainer.style.fontFamily = 'Arial, sans-serif';
      tempContainer.style.fontSize = '14px';
      tempContainer.style.lineHeight = '1.6';
      tempContainer.style.color = '#333';

      // Add header
      const header = document.createElement('div');
      header.style.borderBottom = '2px solid #ccc';
      header.style.paddingBottom = '20px';
      header.style.marginBottom = '30px';
      header.innerHTML = `
        <h1 style="margin: 0 0 10px 0; font-size: 24px; color: #2563eb;">Rapor</h1>
        ${patientName ? `<p style="margin: 5px 0;"><strong>Hasta:</strong> ${patientName}</p>` : ''}
        ${imageFilename ? `<p style="margin: 5px 0;"><strong>Görüntü:</strong> ${imageFilename}</p>` : ''}
        <p style="margin: 5px 0;"><strong>Tarih:</strong> ${new Date().toLocaleDateString('tr-TR')}</p>
      `;

      // Add content
      const content = document.createElement('div');
      content.innerHTML = htmlContent;
      content.style.minHeight = '400px';

      tempContainer.appendChild(header);
      tempContainer.appendChild(content);
      document.body.appendChild(tempContainer);

      // Convert to canvas
      const canvas = await html2canvas(tempContainer, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      });

      // Remove temporary container
      document.body.removeChild(tempContainer);

      // Create PDF
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');
      
      const imgWidth = 210; // A4 width in mm
      const pageHeight = 295; // A4 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      // Add first page
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Add additional pages if needed
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Download the PDF
      pdf.save(filename);
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw new Error('PDF oluşturulurken bir hata oluştu');
    }
  }

  /**
   * Generate a simple text-based PDF from plain text
   */
  static downloadTextAsPDF(
    text: string,
    filename: string,
    patientName?: string,
    imageFilename?: string
  ): void {
    try {
      const pdf = new jsPDF('p', 'mm', 'a4');
      
      // Set font
      pdf.setFont('helvetica', 'normal');
      
      // Add header
      pdf.setFontSize(20);
      pdf.setTextColor(37, 99, 235); // Blue color
      pdf.text('Rapor', 20, 30);
      
      pdf.setFontSize(12);
      pdf.setTextColor(0, 0, 0); // Black color
      
      let yPosition = 50;
      
      if (patientName) {
        pdf.text(`Hasta: ${patientName}`, 20, yPosition);
        yPosition += 10;
      }
      
      if (imageFilename) {
        pdf.text(`Görüntü: ${imageFilename}`, 20, yPosition);
        yPosition += 10;
      }
      
      pdf.text(`Tarih: ${new Date().toLocaleDateString('tr-TR')}`, 20, yPosition);
      yPosition += 20;
      
      // Add a line
      pdf.setDrawColor(204, 204, 204);
      pdf.line(20, yPosition, 190, yPosition);
      yPosition += 20;
      
      // Add content
      pdf.setFontSize(11);
      const splitText = pdf.splitTextToSize(text, 170);
      pdf.text(splitText, 20, yPosition);
      
      // Download the PDF
      pdf.save(filename);
    } catch (error) {
      console.error('Error generating text PDF:', error);
      throw new Error('PDF oluşturulurken bir hata oluştu');
    }
  }
}
